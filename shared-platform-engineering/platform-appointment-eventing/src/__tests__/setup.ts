import { vi, beforeEach, afterEach } from 'vitest';

// Mock crypto module for UUID generation
vi.mock('node:crypto', () => ({
  randomUUID: vi.fn(() => 'test-uuid-123'),
}));

// Suppress console.error during tests to reduce noise from intentional error scenarios
const originalConsoleError = console.error;
beforeEach(() => {
  console.error = vi.fn();
});

afterEach(() => {
  console.error = originalConsoleError;
});

// Create a shared jetstream mock that persists across calls
const mockJetStreamClient = {
  consumers: vi.fn(() => ({
    get: vi.fn(),
  })),
  publish: vi.fn(),
  streams: vi.fn(() => ({
    get: vi.fn(),
  })),
  subscribe: vi.fn(() => ({
    ack: vi.fn(),
    closed: Promise.resolve(),
    drain: vi.fn(),
    error: vi.fn(),
    isClosed: vi.fn(() => false),
    nak: vi.fn(),
    next: vi.fn(),
    unsubscribe: vi.fn(),
  })),
};

// Mock NATS connection
vi.mock('nats', () => ({
  connect: vi.fn(() => ({
    close: vi.fn(),
    closed: vi.fn(() => Promise.resolve()),
    drain: vi.fn(),
    flush: vi.fn(),
    info: vi.fn(() => ({ jetstream: true })),
    jetstream: vi.fn(() => mockJetStreamClient),
    jetstreamManager: vi.fn(() => ({
      streams: {
        add: vi.fn(),
        get: vi.fn(),
        info: vi.fn(),
        update: vi.fn(),
      },
    })),
    publish: vi.fn(),
    request: vi.fn(),
    status: vi.fn(() => 'connected'),
    subscribe: vi.fn(() => ({
      closed: Promise.resolve(),
      drain: vi.fn(),
      error: vi.fn(),
      isClosed: vi.fn(() => false),
      next: vi.fn(),
      unsubscribe: vi.fn(),
      [Symbol.asyncIterator]: async function* () {
        // Mock async iterator for message processing
      },
    })),
  })),
  StringCodec: vi.fn(() => ({
    encode: vi.fn((str) => new TextEncoder().encode(str)),
    decode: vi.fn((data) => new TextDecoder().decode(data)),
  })),
  JSONCodec: vi.fn(() => ({
    encode: vi.fn((obj) => new TextEncoder().encode(JSON.stringify(obj))),
    decode: vi.fn((data) => JSON.parse(new TextDecoder().decode(data))),
  })),
  headers: vi.fn(() => ({
    set: vi.fn(),
    get: vi.fn(),
    has: vi.fn(),
    delete: vi.fn(),
  })),
  RetentionPolicy: {
    Limits: 'limits',
    Interest: 'interest',
    Workqueue: 'workqueue',
  },
  StorageType: {
    File: 'file',
    Memory: 'memory',
  },
}));

// Export the shared mock for use in tests
export { mockJetStreamClient };
