import { describe, expect, it } from 'vitest';
import { v4 as uuidv4 } from 'uuid';
import type { Appointment } from '@beauty-crm/product-appointment-types/src/appointment/types';
import {
  createAppointmentCancelledEvent,
  createAppointmentCreatedEvent,
  createAppointmentUpdatedEvent,
  type AppointmentEvent,
} from '../events';

// Mock appointment data for testing
const mockAppointment: Appointment = {
  id: 'appointment-123',
  salonId: 'salon-456',
  customerId: 'customer-789',
  treatmentId: 'treatment-101',
  staffId: 'staff-202',
  timeSlot: {
    start: new Date('2024-01-15T10:00:00Z'),
    end: new Date('2024-01-15T11:00:00Z'),
  },
  status: 'CONFIRMED',
  priority: 'medium',
  notes: 'Regular appointment',
  createdAt: new Date('2024-01-01T00:00:00Z'),
  updatedAt: new Date('2024-01-01T00:00:00Z'),
};

const mockEventOptions = {
  source: 'appointment-service',
  correlationId: 'corr-123',
  userId: 'user-456',
};

describe('Appointment Events', () => {
  describe('createAppointmentCreatedEvent', () => {
    it('should create a valid appointment created event', () => {
      const event = createAppointmentCreatedEvent(mockAppointment, mockEventOptions);

      expect(event).toMatchObject({
        eventType: 'appointment.created',
        aggregateId: mockAppointment.id,
        aggregateType: 'appointment',
        data: { appointment: mockAppointment },
        source: mockEventOptions.source,
        eventVersion: 1,
        correlationId: mockEventOptions.correlationId,
        userId: mockEventOptions.userId,
      });

      expect(event.eventId).toBeDefined();
      expect(event.timestamp).toBeDefined();
    });

    it('should create event without optional metadata', () => {
      const event = createAppointmentCreatedEvent(mockAppointment, {
        source: 'test-service',
      });

      expect(event).toMatchObject({
        eventType: 'appointment.created',
        aggregateId: mockAppointment.id,
        aggregateType: 'appointment',
        data: { appointment: mockAppointment },
        source: 'test-service',
        eventVersion: 1,
      });

      expect(event.correlationId).toBeUndefined();
      expect(event.userId).toBeUndefined();
    });

    it('should generate unique event IDs', () => {
      const event1 = createAppointmentCreatedEvent(mockAppointment, mockEventOptions);
      const event2 = createAppointmentCreatedEvent(mockAppointment, mockEventOptions);

      expect(event1.eventId).not.toBe(event2.eventId);
    });
  });

  describe('createAppointmentUpdatedEvent', () => {
    it('should create a valid appointment updated event', () => {
      const changes: Partial<Appointment> = {
        status: 'COMPLETED',
        notes: 'Updated notes',
      };

      const event = createAppointmentUpdatedEvent(
        mockAppointment,
        changes,
        mockEventOptions
      );

      expect(event).toMatchObject({
        eventType: 'appointment.updated',
        aggregateId: mockAppointment.id,
        aggregateType: 'appointment',
        data: {
          appointment: mockAppointment,
          changes: changes,
        },
        source: mockEventOptions.source,
        eventVersion: 1,
        correlationId: mockEventOptions.correlationId,
        userId: mockEventOptions.userId,
      });
    });

    it('should handle empty changes', () => {
      const event = createAppointmentUpdatedEvent(
        mockAppointment,
        {},
        mockEventOptions
      );

      expect(event.data.changes).toEqual({});
    });

    it('should include all changed field names', () => {
      const changes: Partial<Appointment> = {
        status: 'CANCELLED',
        notes: 'Cancelled by customer',
        priority: 'high',
      };

      const event = createAppointmentUpdatedEvent(
        mockAppointment,
        changes,
        mockEventOptions
      );

      expect(event.data.changes).toEqual(changes);
    });
  });

  describe('createAppointmentCancelledEvent', () => {
    it('should create a valid appointment cancelled event', () => {
      const appointmentId = 'appointment-123';
      const reason = 'Customer requested cancellation';

      const event = createAppointmentCancelledEvent(
        appointmentId,
        reason,
        mockEventOptions
      );

      expect(event).toMatchObject({
        eventType: 'appointment.cancelled',
        aggregateId: appointmentId,
        aggregateType: 'appointment',
        source: mockEventOptions.source,
        eventVersion: 1,
        correlationId: mockEventOptions.correlationId,
        userId: mockEventOptions.userId,
      });

      expect(event.data).toEqual({
        appointmentId,
        reason,
      });
    });

    it('should handle cancellation without optional metadata', () => {
      const appointmentId = 'appointment-456';
      const reason = 'Staff unavailable';

      const event = createAppointmentCancelledEvent(appointmentId, reason, {
        source: 'test-service',
      });

      expect(event).toMatchObject({
        eventType: 'appointment.cancelled',
        aggregateId: appointmentId,
        aggregateType: 'appointment',
        source: 'test-service',
        eventVersion: 1,
      });

      expect(event.data).toEqual({
        appointmentId,
        reason,
      });
    });
  });

  describe('Event structure validation', () => {
    it('should have consistent event structure across all event types', () => {
      const createdEvent = createAppointmentCreatedEvent(mockAppointment, mockEventOptions);
      const updatedEvent = createAppointmentUpdatedEvent(
        mockAppointment,
        { status: 'COMPLETED' },
        mockEventOptions
      );
      const cancelledEvent = createAppointmentCancelledEvent(
        'appointment-123',
        'test reason',
        mockEventOptions
      );

      const events = [createdEvent, updatedEvent, cancelledEvent];

      for (const event of events) {
        expect(event).toHaveProperty('eventId');
        expect(event).toHaveProperty('eventType');
        expect(event).toHaveProperty('aggregateId');
        expect(event).toHaveProperty('aggregateType', 'appointment');
        expect(event).toHaveProperty('data');
        expect(event).toHaveProperty('timestamp');
        expect(event).toHaveProperty('eventVersion', 1);
        expect(event).toHaveProperty('source');
        // Options are spread directly into the event, not under metadata
        expect(event).toHaveProperty('correlationId');
        expect(event).toHaveProperty('userId');
      }
    });

    it('should generate ISO timestamp strings', () => {
      const event = createAppointmentCreatedEvent(mockAppointment, mockEventOptions);
      
      expect(event.timestamp).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/);
      expect(new Date(event.timestamp).toISOString()).toBe(event.timestamp);
    });
  });
});
