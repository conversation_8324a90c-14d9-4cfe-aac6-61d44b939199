{"author": "", "dependencies": {"@beauty-crm/platform-eventing": "workspace:*", "@beauty-crm/product-appointment-types": "workspace:*"}, "description": "Shared eventing infrastructure for appointment-related events across the platform.", "devDependencies": {"rimraf": "^6.0.1", "typescript": "^5.8.3"}, "keywords": ["events", "appointments", "nats", "outbox"], "license": "ISC", "main": "dist/index.js", "name": "@beauty-crm/platform-appointment-eventing", "peerDependencies": {"@prisma/client": "^6.11.0"}, "scripts": {"build": "../../node_modules/.bin/tsc", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "../../node_modules/.bin/tsc --watch", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "test": "echo 'No tests specified for platform-appointment-eventing' && exit 0"}, "types": "dist/index.d.ts", "version": "1.0.0"}