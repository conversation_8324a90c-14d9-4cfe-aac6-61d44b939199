import { PrismaTransactionalOutbox as BasePrismaTransactionalOutbox } from '@beauty-crm/platform-appointment-eventing';
import type { AppointmentEvent } from '@beauty-crm/product-appointment-types';
import type { Prisma, PrismaClient } from '@prisma/client';
import type { TransactionalOutbox } from '../../domain/ports/AppointmentEventing';

/**
 * @class PrismaTransactionalOutbox
 * @description A Prisma-based implementation of the TransactionalOutbox using the platform library.
 * It saves events to the database as part of a larger transaction,
 * ensuring that events are only queued if the core business logic succeeds.
 */
export class PrismaTransactionalOutbox implements TransactionalOutbox {
  private outbox: BasePrismaTransactionalOutbox;

  constructor(prismaClient: PrismaClient, tableName = 'appointmentOutbox') {
    this.outbox = new BasePrismaTransactionalOutbox(prismaClient, tableName);
  }

  /**
   * Adds an event to the outbox within a transaction.
   *
   * @param {AppointmentEvent} event
   * @param {Prisma.TransactionClient} tx
   * @returns {Promise<void>}
   */
  public async add(
    event: AppointmentEvent,
    tx: Prisma.TransactionClient
  ): Promise<void> {
    await this.outbox.add(event, tx);
  }

  /**
   * Process pending events from the outbox.
   *
   * @param processFn Function to process each event
   * @param batchSize Number of events to process in each batch
   * @param maxRetries Maximum number of retries for failed events
   * @returns Promise with processing results
   */
  public async processPendingEvents(
    processFn: (event: AppointmentEvent) => Promise<void>,
    batchSize = 50,
    maxRetries = 3
  ): Promise<{ processed: number; failed: number }> {
    return this.outbox.processPendingEvents(processFn, batchSize, maxRetries);
  }
}
