import { EventPublisher, PublisherConfigs } from '@beauty-crm/platform-eventing';
import { PrismaClient } from '@prisma/client';
import { AppointmentEventPublisherImpl } from './AppointmentEventPublisherImpl';
import { PrismaTransactionalOutbox } from './PrismaTransactionalOutbox';

/**
 * @class EventingContainer
 * @description Dependency injection container for eventing infrastructure.
 * Provides configured instances of event publishers and outbox components.
 */
export class EventingContainer {
  private static instance: EventingContainer;
  private eventPublisher: EventPublisher;
  private appointmentEventPublisher: AppointmentEventPublisherImpl;
  private transactionalOutbox: PrismaTransactionalOutbox;
  private prismaClient: PrismaClient;

  private constructor() {
    // Initialize Prisma client
    this.prismaClient = new PrismaClient();

    // Initialize event publisher with appointment configuration
    this.eventPublisher = new EventPublisher(
      PublisherConfigs.appointment('appointment-management-backend'),
    );

    // Initialize transactional outbox
    this.transactionalOutbox = new PrismaTransactionalOutbox(
      this.prismaClient,
      'appointmentOutbox',
    );

    // Initialize appointment event publisher
    this.appointmentEventPublisher = new AppointmentEventPublisherImpl(
      this.eventPublisher,
      this.transactionalOutbox,
    );
  }

  /**
   * Gets the singleton instance of the eventing container.
   */
  public static getInstance(): EventingContainer {
    if (!EventingContainer.instance) {
      EventingContainer.instance = new EventingContainer();
    }
    return EventingContainer.instance;
  }

  /**
   * Gets the configured event publisher.
   */
  public getEventPublisher(): EventPublisher {
    return this.eventPublisher;
  }

  /**
   * Gets the configured appointment event publisher.
   */
  public getAppointmentEventPublisher(): AppointmentEventPublisherImpl {
    return this.appointmentEventPublisher;
  }

  /**
   * Gets the configured transactional outbox.
   */
  public getTransactionalOutbox(): PrismaTransactionalOutbox {
    return this.transactionalOutbox;
  }

  /**
   * Gets the Prisma client instance.
   */
  public getPrismaClient(): PrismaClient {
    return this.prismaClient;
  }

  /**
   * Initializes the eventing infrastructure (connects to NATS).
   */
  public async initialize(): Promise<void> {
    try {
      await this.eventPublisher.connect();
      console.log('Eventing infrastructure initialized successfully');
    } catch (error) {
      console.error('Failed to initialize eventing infrastructure:', error);
      throw error;
    }
  }

  /**
   * Shuts down the eventing infrastructure gracefully.
   */
  public async shutdown(): Promise<void> {
    try {
      await this.eventPublisher.disconnect();
      await this.prismaClient.$disconnect();
      console.log('Eventing infrastructure shut down successfully');
    } catch (error) {
      console.error('Error during eventing infrastructure shutdown:', error);
      throw error;
    }
  }

  /**
   * Health check for the eventing infrastructure.
   */
  public async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    details: {
      nats: 'connected' | 'disconnected';
      database: 'connected' | 'disconnected';
    };
  }> {
    const details = {
      nats: 'disconnected' as const,
      database: 'disconnected' as const,
    };

    try {
      // Check NATS connection
      const natsStatus = this.eventPublisher.getStatus();
      details.nats = natsStatus === 'connected' ? 'connected' : 'disconnected';

      // Check database connection
      await this.prismaClient.$queryRaw`SELECT 1`;
      details.database = 'connected';
    } catch (error) {
      console.error('Health check failed:', error);
    }

    const status = details.nats === 'connected' && details.database === 'connected'
      ? 'healthy'
      : 'unhealthy';

    return { status, details };
  }
}

/**
 * Convenience function to get the eventing container instance.
 */
export function getEventingContainer(): EventingContainer {
  return EventingContainer.getInstance();
}
