import {
  fromPrismaStatus,
  toPrismaStatus,
} from '@beauty-crm/product-appointment-types';
import type { Prism<PERSON>, PrismaClient } from '@prisma/client';
import type { AppointmentStatus as DomainAppointmentStatus } from '../../domain/models/AppointmentModel';
import { Appointment } from '../../domain/models/AppointmentModel';
import type { AppointmentRepository } from '../../domain/repositories/appointment-repository';

export class PrismaAppointmentRepository implements AppointmentRepository {
  constructor(private prisma: PrismaClient) {}
  async create(appointment: Appointment): Promise<Appointment> {
    const created = await this.prisma.appointment.create({
      data: {
        clientId: appointment.customerId,
        endTime: appointment.endTime,
        notes: appointment.notes || undefined,
        salonId: appointment.salonId,
        staffId: appointment.staffId || undefined,
        startTime: appointment.startTime,
        status: toPrismaStatus(appointment.status as DomainAppointmentStatus),
        treatmentId: appointment.treatmentId,
      },
    });

    return new Appointment({
      createdAt: created.createdAt,
      customerEmail: created.customerEmail || undefined,
      customerId: created.customerId,
      customerName: created.customerName || undefined,
      customerPhone: created.customerPhone || undefined,
      endTime: created.endTime,
      id: created.id,
      notes: created.notes || '',
      salonId: created.salonId,
      staffId: created.staffId,
      startTime: created.startTime,
      status: fromPrismaStatus(created.status),
      treatmentId: created.treatmentId,
      updatedAt: created.updatedAt,
    });
  }

  async findById(id: string): Promise<Appointment | null> {
    const appointment = await this.prisma.appointment.findUnique({
      include: {
        client: true,
      },
      where: { id },
    });

    if (!appointment) return null;

    return new Appointment({
      createdAt: appointment.createdAt,
      customerEmail: appointment.client?.email || undefined,
      customerId: appointment.clientId,
      customerName: appointment.client?.name || undefined,
      customerPhone: appointment.client?.phone || undefined,
      endTime: appointment.endTime,
      id: appointment.id,
      notes: appointment.notes || '',
      salonId: appointment.salonId,
      staffId: appointment.staffId,
      startTime: appointment.startTime,
      status: fromPrismaStatus(appointment.status),
      treatmentId: appointment.treatmentId,
      updatedAt: appointment.updatedAt,
    });
  }

  async findAll(): Promise<Appointment[]> {
    const appointments = await this.prisma.appointment.findMany({
      orderBy: { startTime: 'asc' },
    });

    return appointments.map(
      (appointment) =>
        new Appointment({
          createdAt: appointment.createdAt,
          customerEmail: appointment.customerEmail || undefined,
          customerId: appointment.customerId,
          customerName: appointment.customerName || undefined,
          customerPhone: appointment.customerPhone || undefined,
          endTime: appointment.endTime,
          id: appointment.id,
          notes: appointment.notes || '',
          salonId: appointment.salonId,
          staffId: appointment.staffId,
          startTime: appointment.startTime,
          status: appointment.status,
          treatmentId: appointment.treatmentId,
          updatedAt: appointment.updatedAt,
        })
    );
  }

  async findByStaffId(staffId: string): Promise<Appointment[]> {
    const appointments = await this.prisma.appointment.findMany({
      where: { staffId },
    });

    return appointments.map(
      (appointment) =>
        new Appointment({
          createdAt: appointment.createdAt,
          customerEmail: appointment.customerEmail || undefined,
          customerId: appointment.customerId,
          customerName: appointment.customerName || undefined,
          customerPhone: appointment.customerPhone || undefined,
          endTime: appointment.endTime,
          id: appointment.id,
          notes: appointment.notes || '',
          salonId: appointment.salonId,
          staffId: appointment.staffId,
          startTime: appointment.startTime,
          status: appointment.status,
          treatmentId: appointment.treatmentId,
          updatedAt: appointment.updatedAt,
        })
    );
  }

  async findByCustomerId(customerId: string): Promise<Appointment[]> {
    const appointments = await this.prisma.appointment.findMany({
      where: { customerId },
    });

    return appointments.map(
      (appointment) =>
        new Appointment({
          createdAt: appointment.createdAt,
          customerEmail: appointment.customerEmail || undefined,
          customerId: appointment.customerId,
          customerName: appointment.customerName || undefined,
          customerPhone: appointment.customerPhone || undefined,
          endTime: appointment.endTime,
          id: appointment.id,
          notes: appointment.notes || '',
          salonId: appointment.salonId,
          staffId: appointment.staffId,
          startTime: appointment.startTime,
          status: appointment.status,
          treatmentId: appointment.treatmentId,
          updatedAt: appointment.updatedAt,
        })
    );
  }

  async update(appointment: Appointment): Promise<Appointment> {
    const updated = await this.prisma.appointment.update({
      data: {
        customerEmail: appointment.customerEmail,
        customerId: appointment.customerId,
        customerName: appointment.customerName,
        customerPhone: appointment.customerPhone,
        endTime: appointment.endTime,
        notes: appointment.notes,
        salonId: appointment.salonId,
        staffId: appointment.staffId,
        startTime: appointment.startTime,
        status: toPrismaStatus(appointment.status),
        treatmentId: appointment.treatmentId,
      },
      where: { id: appointment.id || undefined },
    });

    return new Appointment({
      createdAt: updated.createdAt,
      customerEmail: updated.customerEmail || undefined,
      customerId: updated.customerId,
      customerName: updated.customerName || undefined,
      customerPhone: updated.customerPhone || undefined,
      endTime: updated.endTime,
      id: updated.id,
      notes: updated.notes || '',
      salonId: updated.salonId,
      staffId: updated.staffId,
      startTime: updated.startTime,
      status: fromPrismaStatus(updated.status),
      treatmentId: updated.treatmentId,
      updatedAt: updated.updatedAt,
    });
  }

  async delete(id: string): Promise<void> {
    await this.prisma.appointment.delete({
      where: { id },
    });
  }

  async findConflictingAppointments(
    staffId: string,
    startTime: Date,
    endTime: Date
  ): Promise<Appointment[]> {
    const appointments = await this.prisma.$queryRaw<
      Array<{
        id: string;
        salonId: string;
        customerId: string;
        customerEmail: string | null;
        customerName: string | null;
        customerPhone: string | null;
        staffId: string;
        treatmentId: string;
        startTime: Date;
        endTime: Date;
        status: string;
        notes: string | null;
        createdAt: Date;
        updatedAt: Date;
      }>
    >`
      SELECT
        id,
        "salonId",
        "customerId",
        "customerEmail",
        "customerName",
        "customerPhone",
        "staffId",
        "treatmentId",
        "startTime",
        "endTime",
        status,
        notes,
        "createdAt",
        "updatedAt"
      FROM "public"."Appointment"
      WHERE "staffId" = ${staffId}
      AND "id" != ${startTime.toISOString()} -- Exclude current appointment if updating
      AND (
        (${startTime} >= "startTime" AND ${startTime} < "endTime") OR
        (${endTime} > "startTime" AND ${endTime} <= "endTime") OR
        (${startTime} <= "startTime" AND ${endTime} >= "endTime")
      )
    `;

    return appointments.map(
      (appointment) =>
        new Appointment({
          createdAt: appointment.createdAt,
          customerEmail: appointment.customerEmail || undefined,
          customerId: appointment.customerId,
          customerName: appointment.customerName || undefined,
          customerPhone: appointment.customerPhone || undefined,
          endTime: appointment.endTime,
          id: appointment.id,
          notes: appointment.notes || '',
          salonId: appointment.salonId,
          staffId: appointment.staffId,
          startTime: appointment.startTime,
          status: appointment.status,
          treatmentId: appointment.treatmentId,
          updatedAt: appointment.updatedAt,
        })
    );
  }
}
