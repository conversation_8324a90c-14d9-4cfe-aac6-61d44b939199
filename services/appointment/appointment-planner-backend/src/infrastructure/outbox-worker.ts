import {
  EventPublisher,
  PublisherConfigs,
} from '@beauty-crm/platform-eventing';
import { PrismaTransactionalOutbox } from '@beauty-crm/platform-appointment-eventing';
import type { AppointmentEvent } from '@beauty-crm/product-appointment-types';
import { PrismaClient } from '@prisma/client';
import { AppointmentEventPublisherImpl } from './events/AppointmentEventPublisherImpl';

const prisma = new PrismaClient();
const eventPublisher = new EventPublisher(
  PublisherConfigs.appointment('appointment-planner-backend')
);
const outbox = new PrismaTransactionalOutbox(prisma, 'outbox_events');
const appointmentEventPublisher = new AppointmentEventPublisherImpl(
  eventPublisher
);

let shuttingDown = false;

async function publishEvent(event: AppointmentEvent): Promise<void> {
  try {
    const appointmentData = extractAppointmentFromEvent(event);

    if (!appointmentData) {
      throw new Error(
        `Unable to extract appointment data from event: ${event.eventType}`
      );
    }

    const metadata = {
      correlationId: event.correlationId,
      userId: event.userId,
    };

    switch (event.eventType) {
      case 'appointment.created':
        await appointmentEventPublisher.publishAppointmentCreated(
          appointmentData,
          metadata
        );
        break;
      case 'appointment.updated':
        const changes = extractChangesFromEvent(event);
        await appointmentEventPublisher.publishAppointmentUpdated(
          appointmentData,
          changes,
          metadata
        );
        break;
      case 'appointment.cancelled':
        const reason = extractReasonFromEvent(event);
        await appointmentEventPublisher.publishAppointmentCancelled(
          appointmentData.id,
          reason,
          metadata
        );
        break;
      default:
        throw new Error(`Unknown event type: ${event.eventType}`);
    }
  } catch (error) {
    console.error(`Failed to publish event ${event.eventId}:`, error);
    throw error;
  }
}

function extractAppointmentFromEvent(event: AppointmentEvent): any {
  if (event.data && typeof event.data === 'object') {
    if ('appointment' in event.data) {
      return event.data.appointment;
    }
    return event.data;
  }
  return null;
}

function extractChangesFromEvent(event: AppointmentEvent): any {
  if (event.data && typeof event.data === 'object' && 'changes' in event.data) {
    return event.data.changes;
  }
  return {};
}

function extractReasonFromEvent(event: AppointmentEvent): string {
  if (event.data && typeof event.data === 'object' && 'reason' in event.data) {
    return String(event.data.reason);
  }
  return 'No reason provided';
}

async function main() {
  console.log('Starting appointment planner outbox worker...');
  await eventPublisher.connect();
  console.log('NATS publisher connected.');

  while (!shuttingDown) {
    try {
      const result = await outbox.processPendingEvents(publishEvent, 50, 3);
      if (result.processed > 0 || result.failed > 0) {
        console.log(
          `Processed ${result.processed} events, ${result.failed} failed.`
        );
      }
    } catch (error) {
      console.error('Error processing outbox events:', error);
    }
    // Wait for a bit before polling again
    await new Promise((resolve) => setTimeout(resolve, 5000));
  }

  console.log('Outbox worker shutting down.');
  await eventPublisher.disconnect();
  await prisma.$disconnect();
}

function gracefulShutdown() {
  if (shuttingDown) return;
  shuttingDown = true;
  console.log('Received shutdown signal. Finishing current batch...');
}

process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);

main().catch((err) => {
  console.error('Worker crashed:', err);
  process.exit(1);
});
