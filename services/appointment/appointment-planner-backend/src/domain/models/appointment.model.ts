import type { UnifiedAppointment } from '@beauty-crm/product-appointment-types';
import type {
  Appointment,
  AppointmentStatus,
  Prisma,
  PrismaClient,
} from '@prisma/client';
import { createAppointmentUpdatedEvent } from '@beauty-crm/platform-appointment-eventing';

const fromPlannerStatusToUnified = (
  status: AppointmentStatus
): UnifiedAppointment['status'] => {
  switch (status) {
    case 'PENDING':
      return 'PENDING';
    case 'CONFIRMED':
      return 'CONFIRMED';
    case 'CANCELLED':
      return 'CANCELLED';
    case 'COMPLETED':
      return 'COMPLETED';
    default:
      return 'UNKNOWN';
  }
};

const toUnifiedAppointment = (appointment: Appointment): UnifiedAppointment => {
  return {
    createdAt: appointment.createdAt,
    customerEmail: appointment.customerEmail, // Using email as customerId for now
    customerId: appointment.customerEmail,
    customerName: appointment.customerName,
    customerPhone: appointment.customerPhone || '',
    endTime: appointment.endTime,
    id: appointment.id,
    salonId: appointment.salonId,
    salonName: appointment.salonName, // Using treatment name as ID for now
    source: 'PLANNER',
    staffId: appointment.staffId || '',
    startTime: appointment.startTime,
    status: fromPlannerStatusToUnified(appointment.status),
    treatmentDuration: appointment.treatmentDuration,
    treatmentId: appointment.treatmentName,
    treatmentName: appointment.treatmentName,
    treatmentPrice: appointment.treatmentPrice,
    updatedAt: appointment.updatedAt,
  };
};

export interface CreateAppointmentData {
  salonId: string;
  salonName: string;
  salonLogo?: string;
  salonColor?: string;
  customerName: string;
  customerEmail: string;
  customerPhone?: string;
  treatmentName: string;
  treatmentDuration: number;
  treatmentPrice: number;
  staffId?: string;
  startTime: Date;
  endTime: Date;
  notes?: string;
  status?: AppointmentStatus;
  locale?: string;
}

export interface UpdateAppointmentData {
  status?: AppointmentStatus;
  startTime?: Date;
  endTime?: Date;
  servicePrice?: number;
}

export class AppointmentModel {
  constructor(private prisma: PrismaClient) {}

  async createAppointment(data: CreateAppointmentData): Promise<Appointment> {
    const createdAppointment = await this.prisma.appointment.create({
      data: {
        customerEmail: data.customerEmail,
        customerName: data.customerName,
        customerPhone: data.customerPhone,
        endTime: data.endTime,
        locale: data.locale || 'en',
        notes: data.notes,
        salonColor: data.salonColor,
        salonId: data.salonId,
        salonLogo: data.salonLogo,
        salonName: data.salonName,
        staffId: data.staffId,
        startTime: data.startTime,
        status: data.status || 'PENDING',
        treatmentDuration: data.treatmentDuration,
        treatmentName: data.treatmentName,
        treatmentPrice: data.treatmentPrice,
      },
    });

    await this.prisma.appointmentOutbox.create({
      data: createOutboxEvent(
        toUnifiedAppointment(createdAppointment),
        'appointment.created',
        'appointment-planner-backend'
      ),
    });

    return createdAppointment;
  }

  async getAppointmentById(id: string): Promise<Appointment | null> {
    return this.prisma.appointment.findUnique({
      where: { id },
    });
  }

  async getAppointmentsByCustomer(
    customerEmail: string
  ): Promise<Appointment[]> {
    return this.prisma.appointment.findMany({
      orderBy: { startTime: 'asc' },
      where: { customerEmail },
    });
  }

  async getAppointmentsBySalon(
    salonId: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<Appointment[]> {
    const where: Prisma.AppointmentWhereInput = { salonId };

    if (startDate || endDate) {
      where.startTime = {};
      if (startDate) where.startTime.gte = startDate;
      if (endDate) where.startTime.lte = endDate;
    }

    return this.prisma.appointment.findMany({
      orderBy: { startTime: 'asc' },
      where,
    });
  }

  async updateAppointment(
    id: string,
    data: UpdateAppointmentData
  ): Promise<Appointment> {
    const existingAppointment = await this.prisma.appointment.findUnique({
      where: { id },
    });

    if (!existingAppointment) {
      throw new Error(`Appointment with ID ${id} not found.`);
    }

    const updatedAppointment = await this.prisma.appointment.update({
      data,
      where: { id },
    });

    // Create and store appointment updated event using platform library
    const event = createAppointmentUpdatedEvent(
      toUnifiedAppointment(updatedAppointment),
      data, // The changes made
      {
        source: 'appointment-planner-backend',
        correlationId: `planner-update-${id}`,
      }
    );

    await this.prisma.outbox_events.create({
      data: {
        eventId: event.eventId,
        eventType: event.eventType,
        aggregateId: event.aggregateId,
        aggregateType: event.aggregateType,
        eventVersion: event.eventVersion,
        eventData: event.data as any,
        metadata: event.metadata as any,
        source: event.source,
        timestamp: new Date(event.timestamp),
      },
    });

    return updatedAppointment;
  }

  async cancelAppointment(id: string): Promise<Appointment> {
    return this.updateAppointment(id, { status: 'CANCELLED' });
  }

  async deleteAppointment(id: string): Promise<void> {
    await this.prisma.appointment.delete({
      where: { id },
    });
  }

  async getAvailableSlots(
    salonId: string,
    treatmentName: string,
    date: Date
  ): Promise<{ startTime: Date; endTime: Date }[]> {
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);

    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);

    const existingAppointments = await this.prisma.appointment.findMany({
      select: {
        endTime: true,
        startTime: true,
      },
      where: {
        salonId,
        startTime: {
          gte: startOfDay,
          lt: endOfDay,
        },
        status: { not: 'CANCELLED' },
        treatmentName,
      },
    });

    // Basic slot generation logic - would be more sophisticated in production
    const slots: { startTime: Date; endTime: Date }[] = [];
    const workingHours = [9, 10, 11, 12, 13, 14, 15, 16, 17]; // 9 AM to 5 PM

    for (const hour of workingHours) {
      const slotStart = new Date(date);
      slotStart.setHours(hour, 0, 0, 0);

      const slotEnd = new Date(slotStart);
      slotEnd.setHours(hour + 1, 0, 0, 0);

      // Check if this slot conflicts with existing appointments
      const hasConflict = existingAppointments.some(
        (appointment: { startTime: Date; endTime: Date }) =>
          slotStart < appointment.endTime && slotEnd > appointment.startTime
      );

      if (!hasConflict) {
        slots.push({ endTime: slotEnd, startTime: slotStart });
      }
    }

    return slots;
  }
}
