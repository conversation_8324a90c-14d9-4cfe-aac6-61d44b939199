{"author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "bugs": {"url": "https://github.com/san4osq/beauty-crm/issues"}, "bun": {"install": {"cache": true, "concurrent": 4, "dev": true, "dryRun": false, "force": false, "frozenLockfile": false, "logLevel": "warn", "optional": false, "peer": true, "production": false, "registry": "https://registry.npmjs.org/", "scopes": {}, "verbose": false}}, "dependencies": {"nats": "^2.29.3", "node-fetch": "^3.3.2"}, "description": "Bun package manager. Multi-tenant Beauty Industry CRM", "devDependencies": {"@biomejs/biome": "^2.0.6", "@biomejs/cli-darwin-x64": "^2.1.1", "@nx/eslint": "^21.2.2", "@nx/jest": "21.1.3", "@nx/node": "^21.2.2", "@nx/playwright": "21.1.3", "@nx/react": "^21.2.2", "@nx/storybook": "21.1.3", "@nx/vite": "^21.2.2", "nx": "21.2.1", "typescript": "^5.8.3"}, "directories": {"doc": "docs"}, "engines": {"node": ">=18.0.0", "yarn": ">=1.22.0"}, "homepage": "https://github.com/san4osq/beauty-crm#readme", "keywords": ["beauty", "crm"], "license": "ISC", "main": "index.js", "name": "@beauty-crm/root", "nx": {}, "private": true, "repository": {"type": "git", "url": "git+https://github.com/san4osq/beauty-crm.git"}, "resolutions": {"@biomejs/biome": "2.0.6", "@storybook/react": "^9.0.9", "@storybook/react-vite": "^9.0.9", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "esbuild": "npm:esbuild-wasm", "react": "19.1.0", "react-dom": "19.1.0", "rollup": "npm:@rollup/wasm-node", "storybook": "^9.0.9", "typescript": "^5.8.3", "vitest": "^3.2.2"}, "scripts": {"biome:ci": "bunx @biomejs/biome ci .", "biome:migrate": "bunx @biomejs/biome migrate", "build": "bun run build:shared:ordered", "build:neural-mcp": "cd shared-platform-engineering/neural-mcp && bun run build", "build:platform": "bun run --filter '@beauty-crm/platform-*' build", "build:platform-db": "cd shared-platform-engineering/platform-db-client && bun run build", "build:platform-env": "cd shared-platform-engineering/platform-environment-names && bun run build", "build:platform-eventing": "cd shared-platform-engineering/platform-eventing && bun run build", "build:platform-identity": "cd shared-platform-engineering/platform-identity-client && bun run build", "build:platform-lifecycle": "cd shared-platform-engineering/platform-computing-lifecycle && bun run build", "build:platform-logger": "cd shared-platform-engineering/platform-logger && bun run build", "build:platform-mdblaster": "cd shared-platform-engineering/platform-mdblaster && bun run build", "build:platform-roadmap": "cd shared-platform-engineering/platform-roadmap-generator && bun run build", "build:platform-runtime": "cd shared-platform-engineering/platform-computing-runtime && bun run build", "build:platform-shell": "cd shared-platform-engineering/platform-shell-lifecycle && bun run build", "build:platform-system": "cd shared-platform-engineering/platform-system-settings && bun run build", "build:platform-test-runner": "cd shared-platform-engineering/platform-test-runner && bun run build", "build:platform-ui": "cd shared-platform-engineering/platform-introvertic-ui && bun run build", "build:platform-utilities": "cd shared-platform-engineering/platform-utilities && bun run build", "build:product": "bun run --filter '@beauty-crm/product-*' build", "build:product-db": "cd shared-product-engineering/product-db-names && bun run build", "build:product-domain": "cd shared-product-engineering/product-domain-types && bun run build", "build:product-identity": "cd shared-product-engineering/product-identity-types && bun run build", "build:product-kernel": "cd shared-product-engineering/product-kernel && bun run build", "build:product-kv": "cd shared-product-engineering/product-kv-names && bun run build", "build:product-queue": "cd shared-product-engineering/product-queue-names && bun run build", "build:product-responses": "cd shared-product-engineering/product-responses && bun run build", "build:product-translations": "cd shared-product-engineering/product-translations && bun run build", "build:shared": "bun run build:platform && bun run build:product", "build:shared:dependencies": "bun run --filter '@beauty-crm/platform-environment-names' build && bun run --filter '@beauty-crm/platform-utilities' build && bun run --filter '@beauty-crm/platform-logger' build && bun run --filter '@beauty-crm/platform-computing-runtime' build && bun run --filter '@beauty-crm/platform-db-client' build && bun run --filter '@beauty-crm/platform-eventing' build && bun run --filter '@beauty-crm/platform-identity-client' build && bun run --filter '@beauty-crm/platform-introvertic-ui' build && bun run --filter '@beauty-crm/platform-mdblaster' build && bun run --filter '@beauty-crm/platform-roadmap-generator' build && bun run --filter '@beauty-crm/platform-shell-lifecycle' build && bun run --filter '@beauty-crm/platform-system-settings' build && bun run --filter '@beauty-crm/platform-test-runner' build && bun run --filter '@beauty-crm/platform-tsconfig-generator' build", "build:shared:ordered": "bun run build:shared:dependencies && bun run build:product", "build:staff-backend": "cd services/staff/staff-management-backend && bun run build", "dev": "yarn nx serve platform-introvertic-ui", "fix": "bunx @biomejs/biome check --write .", "fix:unsafe": "bunx @biomejs/biome check --write --unsafe .", "format": "./node_modules/@biomejs/cli-darwin-arm64/biome format --write .", "format:all": "bun --filter '*' format", "format:check": "./node_modules/@biomejs/cli-darwin-arm64/biome format --check .", "format:services": "./node_modules/@biomejs/cli-darwin-arm64/biome format --write services", "format:shared": "./node_modules/@biomejs/cli-darwin-arm64/biome format --write shared-platform-engineering shared-product-engineering shared-ddd-layers", "lint": "./node_modules/@biomejs/cli-darwin-arm64/biome lint .", "lint:backend": "bun --filter '*-backend' lint", "lint:backend:fix": "bun --filter '*-backend' lint:fix", "lint:check": "./node_modules/@biomejs/cli-darwin-arm64/biome check .", "lint:fix": "./node_modules/@biomejs/cli-darwin-arm64/biome lint --write .", "lint:frontend": "bun --filter '*-frontend' lint", "lint:frontend:fix": "bun --filter '*-frontend' lint:fix", "lint:services": "./node_modules/@biomejs/cli-darwin-arm64/biome lint services", "lint:services:fix": "./node_modules/@biomejs/cli-darwin-arm64/biome lint --write services", "lint:targeted": "bun biome lint --write services/appointment services/salon services/staff services/planner services/client services/treatment shared-platform-engineering shared-product-engineering", "nx": "nx", "test": "bun run test platform-introvertic-ui"}, "trustedDependencies": ["@nx/nx-darwin-x64", "@nx/nx-darwin-arm64", "@nx/nx-linux-x64-gnu", "@nx/nx-linux-x64-musl", "@nx/nx-win32-x64-msvc", "@nx/nx-linux-arm64-gnu", "@nx/nx-linux-arm64-musl", "@nx/nx-win32-arm64-msvc", "@nx/nx-freebsd-x64", "@nx/nx-linux-arm-gnueabihf"], "type": "module", "version": "1.0.0", "workspaces": ["shared-platform-engineering/*", "shared-product-engineering/*", "shared-ddd-layers/*", "services/*/*-backend", "services/*/*-frontend"]}